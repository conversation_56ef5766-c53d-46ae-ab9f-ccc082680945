<script>
	import Button from '$lib/components/primitives/Button/Button.svelte';
	import Flex from '$lib/components/primitives/Flex/Flex.svelte';
	import Heading from '$lib/components/primitives/Heading/Heading.svelte';
	import Text from '$lib/components/primitives/Text/Text.svelte';
</script>

<Flex style="width: 100%;" justify="center" direction="col" gap="xs">
	<Heading size="hero">De<PERSON> <PERSON><PERSON></Heading>

	<Text as="p">
		Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
	</Text>

	<Button>Test</Button>
</Flex>

<div class="test-group">
	<div class="group">
		<Heading size="hero"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></Heading>
		<Heading size="h1">Dej p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></Heading>
		<Heading size="h2">Dej p<PERSON><PERSON><PERSON></Heading>
		<Heading size="h3">Dej p<PERSON><PERSON><PERSON><PERSON><PERSON>ky</Heading>
		<Heading size="h4">Dej přijímačky</Heading>
	</div>

	<div class="group">
		<Text as="p">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Text as="p" size="small">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Text as="p" italic={true}>
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Text as="p" color="secondary" size="small">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Text as="p" weight="bold">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>

		<Text as="p" align="center">
			Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias blanditiis dolorum ea earum expedita ipsum, molestias nesciunt nisi numquam perferendis quisquam rem tempora ullam ut velit? Aliquid cumque iusto provident?
		</Text>
	</div>

	<div class="group">
		<Button variant="primary" size="large">Test</Button>
		<Button variant="error" size="medium">Test</Button>
		<Button variant="success" size="small">Test</Button>
	</div>
</div>

<style lang="scss">
	.test-group {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: var(--spacing-xl);

		.group {
			display: flex;
			align-items: flex-start;
			flex-direction: column;
			gap: var(--spacing-m);
		}
	}
</style>
